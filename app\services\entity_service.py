"""
实体管理服务

实现风险实体的创建、查询、更新和关系管理等业务逻辑
"""

from datetime import datetime
from typing import List, Optional
from uuid import uuid4

from app.database.connection import Neo4jConnection
from app.repositories.base import BaseRepository
from app.models.entity import (
    EntityCreate,
    EntityUpdate,
    EntityResponse,
    RelationshipCreate,
    RelationshipResponse,
    EntitySearchParams,
    PathResponse
)
from app.models.alert import BatchCreateResponse, AlertDetailResponse
import hashlib
import json


class EntityService:
    """实体管理服务"""
    
    def __init__(self, connection: Neo4jConnection):
        self.connection = connection
        self.repository = BaseRepository(connection)
    
    def find_or_create_entity(self, entity_data: EntityCreate) -> tuple[EntityResponse, bool]:
        """
        查找或创建实体，返回(实体, 是否新创建)
        
        Args:
            entity_data: 实体创建数据
            
        Returns:
            tuple: (实体响应, 是否新创建)
        """
        try:
            # 先尝试查找现有实体
            existing_entity = self._find_existing_entity(entity_data)
            
            if existing_entity:
                # 找到现有实体，更新时间和风险分数
                self._update_entity_on_reuse(existing_entity.entity_id, entity_data)
                return existing_entity, False
            else:
                # 创建新实体
                new_entity = self.create_entity(entity_data)
                return new_entity, True
                
        except Exception as e:
            raise Exception(f"查找或创建实体失败: {str(e)}")

    def create_entity(self, entity_data: EntityCreate) -> EntityResponse:
        """创建实体"""
        try:
            # 生成唯一ID
            entity_id = str(uuid4())
            current_time = datetime.now()
            
            # 准备节点属性
            properties = {
                "entity_id": entity_id,
                "name": entity_data.name,
                "risk_score": entity_data.risk_score,
                "description": entity_data.description,
                "created_at": current_time.isoformat(),
                "updated_at": current_time.isoformat(),
                "reuse_count": 0,
                **entity_data.properties
            }
            
            # 为特定实体类型添加特殊字段
            if entity_data.entity_type.value == "ProcessEntity":
                if "process_name" in entity_data.properties and "command_line" in entity_data.properties:
                    process_signature = f"{entity_data.properties['process_name']}|{entity_data.properties['command_line']}"
                    properties["process_signature"] = hashlib.md5(process_signature.encode()).hexdigest()
            
            # 创建节点，使用实体类型作为标签
            self.repository.create_node(entity_data.entity_type.value, properties)
            
            # 返回响应模型
            return EntityResponse(
                entity_id=entity_id,
                entity_type=entity_data.entity_type,
                name=entity_data.name,
                properties=entity_data.properties,
                risk_score=entity_data.risk_score,
                description=entity_data.description,
                created_at=current_time,
                updated_at=current_time
            )
            
        except Exception as e:
            raise Exception(f"创建实体失败: {str(e)}")
    
    def batch_create_entities(self, entities: List[EntityCreate]) -> BatchCreateResponse:
        """批量创建实体"""
        try:
            success_count = 0
            failed_count = 0
            created_ids = []
            errors = []
            
            for i, entity_data in enumerate(entities):
                try:
                    result = self.create_entity(entity_data)
                    success_count += 1
                    created_ids.append(result.entity_id)
                except Exception as e:
                    failed_count += 1
                    errors.append({
                        "index": i,
                        "entity_type": entity_data.entity_type.value,
                        "error": str(e)
                    })
            
            return BatchCreateResponse(
                success_count=success_count,
                failed_count=failed_count,
                created_ids=created_ids,
                errors=errors
            )
            
        except Exception as e:
            raise Exception(f"批量创建实体失败: {str(e)}")
    
    def get_entity_by_id(self, entity_id: str) -> Optional[EntityResponse]:
        """根据ID获取实体"""
        try:
            query = """
            MATCH (e {entity_id: $entity_id})
            RETURN e, labels(e) as entity_labels
            """
            
            result = self.connection.execute_read_transaction(
                query, {"entity_id": entity_id}
            )
            
            if not result:
                return None
            
            node_data = result[0]["e"]
            entity_labels = result[0]["entity_labels"]
            
            # 找到实体类型标签（排除通用标签）
            entity_type = None
            for label in entity_labels:
                if label in ["HttpRequestEntity", "HttpResponseEntity", "DnsQueryEntity", 
                            "NetworkEndpointEntity", "UrlEntity", "FileEntity", "ProcessEntity", "Device"]:
                    entity_type = label
                    break
            
            if not entity_type:
                raise Exception("未找到有效的实体类型标签")
            
            return self._convert_to_response(node_data, entity_type)
            
        except Exception as e:
            raise Exception(f"获取实体失败: {str(e)}")
    
    def get_entities_by_type(self, entity_type: str) -> List[EntityResponse]:
        """根据类型获取实体列表"""
        try:
            query = f"""
            MATCH (e:{entity_type})
            RETURN e
            ORDER BY e.created_at DESC
            """
            
            result = self.connection.execute_read_transaction(query)
            
            return [self._convert_to_response(record["e"], entity_type) for record in result]
            
        except Exception as e:
            raise Exception(f"按类型查询实体失败: {str(e)}")
    
    def search_entities(self, search_params: EntitySearchParams) -> List[EntityResponse]:
        """搜索实体"""
        try:
            # 构建查询条件
            where_clauses = []
            params = {"limit": search_params.limit}
            
            if search_params.entity_type:
                entity_type = search_params.entity_type.value
                query_start = f"MATCH (e:{entity_type})"
            else:
                query_start = "MATCH (e)"
                entity_type = None
            
            if search_params.properties:
                for key, value in search_params.properties.items():
                    where_clauses.append(f"e.{key} = ${key}")
                    params[key] = value
            
            where_clause = ""
            if where_clauses:
                where_clause = "WHERE " + " AND ".join(where_clauses)
            
            query = f"""
            {query_start}
            {where_clause}
            RETURN e, labels(e) as entity_labels
            ORDER BY e.created_at DESC
            LIMIT $limit
            """
            
            result = self.connection.execute_read_transaction(query, params)
            
            entities = []
            for record in result:
                node_data = record["e"]
                entity_labels = record["entity_labels"]
                
                # 确定实体类型
                if entity_type:
                    current_entity_type = entity_type
                else:
                    current_entity_type = None
                    for label in entity_labels:
                        if label in ["HttpRequestEntity", "HttpResponseEntity", "DnsQueryEntity", 
                                    "NetworkEndpointEntity", "UrlEntity", "FileEntity", "ProcessEntity", "Device"]:
                            current_entity_type = label
                            break
                
                if current_entity_type:
                    entities.append(self._convert_to_response(node_data, current_entity_type))
            
            return entities
            
        except Exception as e:
            raise Exception(f"搜索实体失败: {str(e)}")
    
    def update_entity(
        self, 
        entity_id: str, 
        update_data: EntityUpdate
    ) -> Optional[EntityResponse]:
        """更新实体"""
        try:
            # 构建动态更新语句
            set_clauses = []
            params = {"entity_id": entity_id, "updated_at": datetime.now().isoformat()}
            
            # 处理直接字段
            if update_data.name is not None:
                set_clauses.append("e.name = $name")
                params["name"] = update_data.name
            
            if update_data.risk_score is not None:
                set_clauses.append("e.risk_score = $risk_score")
                params["risk_score"] = update_data.risk_score
            
            if update_data.description is not None:
                set_clauses.append("e.description = $description")
                params["description"] = update_data.description
            
            # 处理properties字段
            if update_data.properties:
                for key, value in update_data.properties.items():
                    set_clauses.append(f"e.{key} = ${key}")
                    params[key] = value
            
            if not set_clauses:
                return self.get_entity_by_id(entity_id)
            
            query = f"""
            MATCH (e {{entity_id: $entity_id}})
            SET {', '.join(set_clauses)}, e.updated_at = $updated_at
            RETURN e, labels(e) as entity_labels
            """
            
            result = self.connection.execute_write_transaction(query, params)
            
            if not result:
                return None
            
            node_data = result[0]["e"]
            entity_labels = result[0]["entity_labels"]
            
            # 确定实体类型
            entity_type = None
            for label in entity_labels:
                if label in ["HttpRequestEntity", "HttpResponseEntity", "DnsQueryEntity", 
                            "NetworkEndpointEntity", "UrlEntity", "FileEntity", "ProcessEntity", "Device"]:
                    entity_type = label
                    break
            
            if not entity_type:
                raise Exception("未找到有效的实体类型标签")
            
            return self._convert_to_response(node_data, entity_type)
            
        except Exception as e:
            raise Exception(f"更新实体失败: {str(e)}")
    
    def create_relationship(self, rel_data: RelationshipCreate) -> RelationshipResponse:
        """创建实体关系"""
        try:
            relationship_id = str(uuid4())
            current_time = datetime.now()
            
            # 准备关系属性
            properties = {
                "relationship_id": relationship_id,
                "created_at": current_time.isoformat(),
                "updated_at": current_time.isoformat()
            }
            
            if rel_data.properties:
                properties.update(rel_data.properties)
            
            # 创建关系
            query = f"""
            MATCH (from_entity {{entity_id: $from_entity_id}})
            MATCH (to_entity {{entity_id: $to_entity_id}})
            CREATE (from_entity)-[r:{rel_data.relationship_type}]->(to_entity)
            SET r += $properties
            RETURN r
            """
            
            result = self.connection.execute_write_transaction(
                query, {
                    "from_entity_id": rel_data.from_entity_id,
                    "to_entity_id": rel_data.to_entity_id,
                    "properties": properties
                }
            )
            
            if not result:
                raise Exception("创建关系失败，实体不存在")
            
            return RelationshipResponse(
                relationship_id=relationship_id,
                relationship_type=rel_data.relationship_type,
                from_entity_id=rel_data.from_entity_id,
                to_entity_id=rel_data.to_entity_id,
                properties=rel_data.properties,
                created_at=current_time,
                updated_at=current_time
            )
            
        except Exception as e:
            raise Exception(f"创建实体关系失败: {str(e)}")
    
    def get_related_alerts(self, entity_id: str) -> List[AlertDetailResponse]:
        """获取实体关联的告警"""
        try:
            query = """
            MATCH (e {entity_id: $entity_id})-[:LINKED_TO]-(a:AlertDetail)
            RETURN a
            ORDER BY a.start_time DESC
            """
            
            result = self.connection.execute_read_transaction(
                query, {"entity_id": entity_id}
            )
            
            # 这里简化返回，实际应该转换为AlertDetailResponse
            # 需要导入AlertService或在此处进行转换
            return []
            
        except Exception as e:
            raise Exception(f"获取实体关联告警失败: {str(e)}")
    
    def get_entity_relationships(self, entity_id: str) -> List[RelationshipResponse]:
        """获取实体的关系"""
        try:
            query = """
            MATCH (e {entity_id: $entity_id})-[r]-(other)
            RETURN r, type(r) as rel_type, 
                   startNode(r).entity_id as from_id,
                   endNode(r).entity_id as to_id
            """
            
            result = self.connection.execute_read_transaction(
                query, {"entity_id": entity_id}
            )
            
            relationships = []
            for record in result:
                rel_data = record["r"]
                rel_type = record["rel_type"]
                from_id = record["from_id"]
                to_id = record["to_id"]
                
                relationships.append(RelationshipResponse(
                    relationship_id=rel_data.get("relationship_id", ""),
                    relationship_type=rel_type,
                    from_entity_id=from_id,
                    to_entity_id=to_id,
                    properties=dict(rel_data) if rel_data else {},
                    created_at=datetime.fromisoformat(rel_data.get("created_at", datetime.now().isoformat())),
                    updated_at=datetime.fromisoformat(rel_data.get("updated_at", datetime.now().isoformat()))
                ))
            
            return relationships
            
        except Exception as e:
            raise Exception(f"获取实体关系失败: {str(e)}")
    
    def find_entity_path(
        self, 
        from_entity_id: str, 
        to_entity_id: str, 
        max_depth: int = 5
    ) -> List[PathResponse]:
        """查找实体间路径"""
        try:
            query = f"""
            MATCH path = (from_entity {{entity_id: $from_entity_id}})-[*1..{max_depth}]-(to_entity {{entity_id: $to_entity_id}})
            RETURN path
            ORDER BY length(path)
            LIMIT 10
            """
            
            result = self.connection.execute_read_transaction(
                query, {
                    "from_entity_id": from_entity_id,
                    "to_entity_id": to_entity_id
                }
            )
            
            paths = []
            for record in result:
                path = record["path"]
                nodes = path.nodes
                relationships = path.relationships
                
                # 转换节点和关系（简化版）
                path_nodes = []
                path_relationships = []
                
                for node in nodes:
                    # 简化的节点转换
                    path_nodes.append({
                        "entity_id": node.get("entity_id"),
                        "properties": dict(node)
                    })
                
                for rel in relationships:
                    # 简化的关系转换
                    path_relationships.append({
                        "relationship_type": rel.type,
                        "properties": dict(rel)
                    })
                
                paths.append(PathResponse(
                    path_length=len(relationships),
                    nodes=path_nodes,
                    relationships=path_relationships
                ))
            
            return paths
            
        except Exception as e:
            raise Exception(f"查找实体路径失败: {str(e)}")
    
    def _convert_to_response(self, node_data: dict, entity_type: str) -> EntityResponse:
        """将Neo4j节点数据转换为响应模型"""
        # 提取基础属性
        entity_id = node_data.get("entity_id")
        name = node_data.get("name")
        risk_score = node_data.get("risk_score", 0)
        description = node_data.get("description")
        created_at = node_data.get("created_at")
        updated_at = node_data.get("updated_at")
        
        # 提取实体属性（排除基础字段）
        properties = {k: v for k, v in node_data.items() 
                     if k not in ["entity_id", "name", "risk_score", "description", "created_at", "updated_at"]}
        
        return EntityResponse(
            entity_id=entity_id,
            entity_type=entity_type,
            name=name,
            properties=properties,
            risk_score=risk_score,
            description=description,
            created_at=datetime.fromisoformat(created_at) if created_at else datetime.now(),
            updated_at=datetime.fromisoformat(updated_at) if updated_at else datetime.now()
        )
    
    def _find_existing_entity(self, entity_data: EntityCreate) -> Optional[EntityResponse]:
        """
        根据实体类型查找现有的匹配实体
        
        Args:
            entity_data: 实体数据
            
        Returns:
            现有实体或None
        """
        try:
            entity_type = entity_data.entity_type.value
            match_criteria = self._get_match_criteria(entity_data)
            
            if not match_criteria:
                return None
            
            # 构建查询条件
            where_clauses = []
            params = {}
            
            for key, value in match_criteria.items():
                where_clauses.append(f"e.{key} = ${key}")
                params[key] = value
            
            where_clause = " AND ".join(where_clauses)
            
            query = f"""
            MATCH (e:{entity_type})
            WHERE {where_clause}
            RETURN e, labels(e) as entity_labels
            LIMIT 1
            """
            
            result = self.connection.execute_read_transaction(query, params)
            
            if result:
                node_data = result[0]["e"]
                return self._convert_to_response(node_data, entity_type)
            
            return None
            
        except Exception as e:
            raise Exception(f"查找现有实体失败: {str(e)}")
    
    def _get_match_criteria(self, entity_data: EntityCreate) -> dict:
        """
        根据实体类型生成匹配条件
        
        Args:
            entity_data: 实体数据
            
        Returns:
            匹配条件字典
        """
        entity_type = entity_data.entity_type.value
        properties = entity_data.properties
        
        if entity_type == "NetworkEndpointEntity":
            # 网络端点：匹配IP地址+端口
            criteria = {}
            if "ip_address" in properties:
                criteria["ip_address"] = properties["ip_address"]
            if "port" in properties:
                criteria["port"] = properties["port"]
            return criteria
            
        elif entity_type == "FileEntity":
            # 文件实体：优先匹配文件哈希，其次文件名
            if "file_hash" in properties and properties["file_hash"]:
                return {"file_hash": properties["file_hash"]}
            elif "filename" in properties and properties["filename"]:
                return {"filename": properties["filename"]}
            
        elif entity_type == "ProcessEntity":
            # 进程实体：匹配进程名+命令行的哈希
            if "process_name" in properties and "command_line" in properties:
                process_signature = f"{properties['process_name']}|{properties['command_line']}"
                process_hash = hashlib.md5(process_signature.encode()).hexdigest()
                return {"process_signature": process_hash}
            elif "process_name" in properties:
                return {"process_name": properties["process_name"]}
                
        elif entity_type == "HttpRequestEntity":
            # HTTP请求：匹配方法+URL+User-Agent的组合
            criteria = {}
            if "method" in properties:
                criteria["method"] = properties["method"]
            if "url" in properties:
                criteria["url"] = properties["url"]
            if "user_agent" in properties:
                criteria["user_agent"] = properties["user_agent"]
            return criteria
            
        elif entity_type == "UrlEntity":
            # URL实体：匹配完整URL
            if "full_url" in properties:
                return {"full_url": properties["full_url"]}
                
        elif entity_type == "DnsQueryEntity":
            # DNS查询：匹配查询域名+查询类型
            criteria = {}
            if "query_name" in properties:
                criteria["query_name"] = properties["query_name"]
            if "query_type" in properties:
                criteria["query_type"] = properties["query_type"]
            return criteria
            
        return {}
    
    def _update_entity_on_reuse(self, entity_id: str, entity_data: EntityCreate):
        """
        当实体被重用时更新其信息
        
        Args:
            entity_id: 实体ID
            entity_data: 新的实体数据
        """
        try:
            current_time = datetime.now()
            
            # 更新时间和可能的风险分数
            update_props = {
                "updated_at": current_time.isoformat(),
                "reuse_count": "COALESCE(e.reuse_count, 0) + 1"
            }
            
            # 如果新的风险分数更高，则更新
            if entity_data.risk_score > 0:
                update_props["risk_score"] = f"CASE WHEN e.risk_score < {entity_data.risk_score} THEN {entity_data.risk_score} ELSE e.risk_score END"
            
            # 构建更新查询
            set_clauses = []
            for key, value in update_props.items():
                if key == "reuse_count" or key.startswith("risk_score"):
                    set_clauses.append(f"e.{key} = {value}")
                else:
                    set_clauses.append(f"e.{key} = '{value}'")
            
            set_clause = ", ".join(set_clauses)
            
            query = f"""
            MATCH (e {{entity_id: $entity_id}})
            SET {set_clause}
            RETURN e
            """
            
            self.connection.execute_write_transaction(query, {"entity_id": entity_id})
            
        except Exception as e:
            raise Exception(f"更新实体重用信息失败: {str(e)}")

    def create_evidence_entity_relationship(
        self, 
        evidence_id: str, 
        entity_id: str, 
        evidence_context: dict
    ) -> str:
        """
        创建证据与实体的关系，记录特征信息
        
        Args:
            evidence_id: 证据ID
            entity_id: 实体ID  
            evidence_context: 证据上下文信息
            
        Returns:
            关系ID
        """
        try:
            relationship_id = str(uuid4())
            current_time = datetime.now()
            
            # 准备关系属性，包含证据特征
            rel_properties = {
                "relationship_id": relationship_id,
                "created_at": current_time.isoformat(),
                "evidence_context": json.dumps(evidence_context, ensure_ascii=False),
                "relationship_type": "EVIDENCE_ENTITY_RELATION"
            }
            
            # 创建关系
            query = """
            MATCH (evidence {evidence_id: $evidence_id})
            MATCH (entity {entity_id: $entity_id})
            CREATE (evidence)-[r:RELATES_TO $rel_props]->(entity)
            RETURN r
            """
            
            self.connection.execute_write_transaction(
                query, 
                {
                    "evidence_id": evidence_id,
                    "entity_id": entity_id,
                    "rel_props": rel_properties
                }
            )
            
            return relationship_id
            
        except Exception as e:
            raise Exception(f"创建证据实体关系失败: {str(e)}") 