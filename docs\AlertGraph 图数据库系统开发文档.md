# AlertGraph 图数据库系统开发文档

## 1. 项目概述

### 1.1 项目简介

AlertGraph是一个基于Neo4j图数据库的安全告警知识图谱系统，用于存储、管理和查询安全告警相关数据，支持三层告警逻辑（告警细节、告警会话、告警事件）的知识图谱构建。

### 1.2 技术栈

- **Python**: 3.12
- **Web框架**: FastAPI 0.115.12
- **图数据库**: Neo4j 5.26.7-community

### 1.3 核心功能

- 告警数据的批量和流式写入
- 多层级告警关系构建
- 风险实体关联分析
- **智能实体去重机制**
- 研判记录管理
- 威胁情报集成
- RESTful API接口服务

## 2. 系统架构设计

### 2.1 核心组件设计

### 2.1.1 数据库连接层

负责Neo4j数据库连接管理、连接池维护、事务处理等基础功能。

### 2.1.2 数据模型层

定义图数据库中的节点和关系模型，包括告警细节、研判记录、风险实体等。

### 2.1.3 数据访问层（Repository Pattern）

封装对Neo4j的CRUD操作，提供面向对象的数据访问接口。

### 2.1.4 业务逻辑层

实现核心业务逻辑，包括告警关联、研判处理、图谱构建等。

### 2.1.5 API层

提供RESTful API接口，支持前端和外部系统调用。

## 3. 数据模型设计

### 3.1 节点类型定义

### 3.1.1 告警细节节点 (AlertDetail)

```python
# 节点标签: AlertDetail
# 基于 app/models/alert.py 中的 AlertDetailBase 模型
Properties:
- vid: STRING (图数据库节点唯一ID，系统自动生成)
- alert_detail_id: STRING (告警细节ID，必填)
- alert_name: STRING (告警名称，必填)
- alert_message: STRING (告警描述，必填)
- vulnerabilities: STRING (漏洞描述，可选)
- product_name: STRING (设备名称，必填)
- time: TIMESTAMP (告警时间，系统自动生成)
- confidence_score: INT (置信度分数 0-100，必填)
- impact: STRING (影响等级: Critical/High/Medium/Low/Unknown，必填)
- risk: STRING (风险等级: Critical/High/Medium/Low/Unknown，必填)
- severity: STRING (严重性: Fatal/Critical/High/Medium/Low/Unknown，必填)
- status: STRING (处理状态: New/InProgress/Suppressed/Resolved/Archived/Unknown，默认New)
- comment: STRING (评论，可选)
- raw_data: STRING (原始推送数据，可选)
- created_at: TIMESTAMP (创建时间，系统自动生成)
- updated_at: TIMESTAMP (更新时间，系统自动生成)
```

**实际实现参考**:
```python
# app/models/alert.py - AlertDetailBase
class AlertDetailBase(BaseModel):
    alert_detail_id: str = Field(..., description="告警细节ID")
    alert_name: str = Field(..., description="告警名称")
    alert_message: str = Field(..., description="告警描述")
    vulnerabilities: Optional[str] = Field(None, description="漏洞描述")
    product_name: str = Field(..., description="设备名称")
    confidence_score: int = Field(..., ge=0, le=100, description="置信度分数 0-100")
    impact: Impact = Field(..., description="影响等级")
    risk: Risk = Field(..., description="风险等级")
    severity: Severity = Field(..., description="严重性")
    status: AlertStatus = Field(AlertStatus.NEW, description="处理状态")
    comment: Optional[str] = Field(None, description="评论")
    raw_data: Optional[str] = Field(None, description="原始推送数据")
```

### 3.1.2 研判记录节点 (Verdict)

```python
# 节点标签: Verdict
# 基于 app/models/alert.py 中的 VerdictBase 模型
Properties:
- verdict_id: STRING (研判记录唯一ID，系统自动生成)
- type_id: INT (研判类型: 1-AI, 2-人工，必填)
- type: STRING (研判类型说明: AI研判/人工研判，必填)
- label: INT (研判标签: 1-误报, 2-确认攻击, 3-可疑, 4-数据不足，必填)
- reason: STRING (研判理由，必填)
- commiter: STRING (研判提交者，必填)
- time: TIMESTAMP (研判时间，系统自动生成)
- comment: STRING (研判评论，可选)
- created_at: TIMESTAMP (创建时间，系统自动生成)
- updated_at: TIMESTAMP (更新时间，系统自动生成)
```

**实际实现参考**:
```python
# app/models/alert.py - VerdictBase
class VerdictBase(BaseModel):
    type_id: VerdictType = Field(..., description="研判类型: 1-AI, 2-人工")
    type: str = Field(..., description="研判类型说明: AI研判/人工研判")
    label: VerdictLabel = Field(..., description="研判标签: 1-误报, 2-确认攻击, 3-可疑, 4-数据不足")
    reason: str = Field(..., description="研判理由")
    commiter: str = Field(..., description="研判提交者")
    comment: Optional[str] = Field(None, description="研判评论")
```

### 3.1.3 证据信息节点 (Evidence)

```python
# 节点标签: Evidence
# 基于 app/services/evidence_service.py 中的实际实现
Properties:
- evidence_id: STRING (证据唯一ID，基于内容生成的UUID5)
- evidence_type: STRING (证据类型，如"combined_evidence")
- evidence_types: LIST[STRING] (所有证据制品的类型列表)
- is_alert_trigger: BOOLEAN (是否包含告警触发证据)
- include_payload: BOOLEAN (是否包含攻击载荷)
- evidence_count: INT (包含的证据制品数量)
- raw_evidence_data: STRING (原始证据数据JSON，包含所有evidence_artifacts)
- created_time: TIMESTAMP (创建时间，系统自动生成)
- updated_time: TIMESTAMP (更新时间，系统自动生成)

# 动态属性（根据证据内容添加）:
- http_methods: LIST[STRING] (HTTP方法列表，如果包含HTTP请求)
- domains: LIST[STRING] (域名列表，如果包含URL或DNS查询)
- ip_addresses: LIST[STRING] (IP地址列表，如果包含网络端点)
- file_hashes: LIST[STRING] (文件哈希列表，如果包含文件信息)
- process_names: LIST[STRING] (进程名称列表，如果包含进程信息)
```

**实际实现参考**:
```python
# app/services/evidence_service.py - _create_evidence_node
properties = {
    "evidence_id": evidence_id,
    "evidence_type": "combined_evidence",
    "evidence_types": evidence_types,
    "is_alert_trigger": has_trigger,
    "include_payload": has_payload,
    "evidence_count": len(evidence_artifacts),
    "raw_evidence_data": json.dumps(all_raw_data, ensure_ascii=False),
    "created_time": current_time.isoformat(),
    "updated_time": current_time.isoformat()
}
```

### 3.1.4 设备信息节点 (Device)

```python
# 节点标签: Device
# 基于 app/models/device.py 中的 DeviceBase 模型
Properties:
- device_id: STRING (设备唯一标识，必填)
- uuid: STRING (设备UUID，可选)
- org: STRING (所属组织，可选)
- ip: STRING (主要IP地址，可选)
- hostname: STRING (主机名，可选)
- mac: STRING (MAC地址，可选)
- os_name: STRING (操作系统名称，可选)
- count: INT (告警数量统计，默认1)
- created_at: TIMESTAMP (创建时间，系统自动生成)
- updated_at: TIMESTAMP (更新时间，系统自动生成)
```

**实际实现参考**:
```python
# app/models/device.py - DeviceBase
class DeviceBase(BaseModel):
    device_id: str = Field(..., description="设备唯一标识")
    uuid: Optional[str] = Field(None, description="设备UUID")
    org: Optional[str] = Field(None, description="所属组织")
    ip: Optional[str] = Field(None, description="主要IP地址")
    hostname: Optional[str] = Field(None, description="主机名")
    mac: Optional[str] = Field(None, description="MAC地址")
    os_name: Optional[str] = Field(None, description="操作系统名称")
    count: Optional[int] = Field(1, description="告警数量统计")
```

**设备ID生成逻辑**:
```python
# app/services/device_service.py - _generate_device_id
# 基于主要标识字段生成设备ID，优先级：hostname > ip > product_name
```

### 3.1.5 风险实体节点

基于 `app/models/base.py` 中的 `EntityType` 枚举和 `app/services/evidence_service.py` 中的实际提取逻辑：

```python
# 支持的实体类型
class EntityType(str, Enum):
    HTTP_REQUEST = "HttpRequestEntity"
    HTTP_RESPONSE = "HttpResponseEntity"
    DNS_QUERY = "DnsQueryEntity"
    NETWORK_ENDPOINT = "NetworkEndpointEntity"
    URL = "UrlEntity"
    FILE = "FileEntity"
    PROCESS = "ProcessEntity"
    DEVICE = "Device"
```

#### ******* 网络端点实体 (NetworkEndpointEntity)
```python
# 节点标签: NetworkEndpointEntity
# 基于 app/services/evidence_service.py 中的 _extract_network_endpoint_entity
Properties:
- entity_id: STRING (实体唯一ID)
- name: STRING (实体名称，通常为IP地址)
- ip_address: STRING (IP地址，主要匹配字段)
- port: INT (端口号，辅助匹配字段)
- hostname: STRING (主机名)
- mac: STRING (MAC地址)
- domain: STRING (域名)
- endpoint_type: STRING (端点类型: src/dst)
- location: OBJECT (地理位置信息，包含country, city, lat, long)
- autonomous_system: OBJECT (AS信息)
- operating_system: OBJECT (操作系统信息)
- reuse_count: INT (复用次数，默认1)
- risk_score: INT (风险分数，0-100)
- description: STRING (实体描述)
- created_at: TIMESTAMP (创建时间)
- updated_at: TIMESTAMP (更新时间)
```

**提取逻辑参考**:
```python
# app/services/evidence_service.py - _extract_network_endpoint_entity
def _extract_network_endpoint_entity(self, endpoint: NetworkEndpointModel, endpoint_type: str) -> Dict[str, Any]:
    if not endpoint.ip:
        return None

    return {
        "entity_type": EntityType.NETWORK_ENDPOINT,
        "name": f"{endpoint.ip}:{endpoint.port}" if endpoint.port else endpoint.ip,
        "properties": {
            "ip_address": endpoint.ip,
            "port": endpoint.port,
            "hostname": endpoint.hostname,
            "mac": endpoint.mac,
            "domain": endpoint.domain,
            "endpoint_type": endpoint_type,
            "location": endpoint.location.model_dump() if endpoint.location else None,
            # ... 其他属性
        },
        "risk_score": self._calculate_endpoint_risk_score(endpoint),
        "description": f"{endpoint_type}端点: {endpoint.ip}"
    }
```

#### ******* 文件实体 (FileEntity)
```python
# 节点标签: FileEntity
# 基于 app/services/evidence_service.py 中的 _extract_file_entity
Properties:
- entity_id: STRING (实体唯一ID)
- name: STRING (实体名称，通常为文件名)
- file_hash: STRING (文件哈希，主要匹配字段)
- filename: STRING (文件名，备选匹配字段)
- file_path: STRING (文件路径)
- file_size: INT (文件大小)
- file_extension: STRING (文件扩展名)
- file_type: STRING (文件类型)
- mime_type: STRING (MIME类型)
- confidentiality: STRING (机密性)
- confidentiality_id: INT (机密性ID)
- accessed_time: TIMESTAMP (文件访问时间)
- created_time: TIMESTAMP (文件创建时间)
- modified_time: TIMESTAMP (文件修改时间)
- hash_md5: STRING (MD5哈希)
- hash_sha1: STRING (SHA1哈希)
- hash_sha256: STRING (SHA256哈希)
- reuse_count: INT (复用次数，默认1)
- risk_score: INT (风险分数，0-100)
- description: STRING (实体描述)
- created_at: TIMESTAMP (实体创建时间)
- updated_at: TIMESTAMP (实体更新时间)
```

**提取逻辑参考**:
```python
# app/services/evidence_service.py - _extract_file_entity
def _extract_file_entity(self, file: FileModel) -> Dict[str, Any]:
    if not file.name and not file.path:
        return None

    # 提取文件哈希（优先SHA256, SHA1, MD5）
    file_hash = None
    if file.hashes:
        for hash_obj in file.hashes:
            if hash_obj.algorithm.upper() == "SHA256":
                file_hash = hash_obj.value
                break
        # 如果没有SHA256，尝试其他哈希
        if not file_hash:
            file_hash = file.hashes[0].value

    return {
        "entity_type": EntityType.FILE,
        "name": file.name or os.path.basename(file.path or ""),
        "properties": {
            "file_hash": file_hash,
            "filename": file.name,
            "file_path": file.path,
            "file_size": getattr(file, 'size', None),
            "file_extension": file.ext,
            "file_type": file.type,
            # ... 其他属性
        },
        "risk_score": self._calculate_file_risk_score(file),
        "description": f"文件: {file.name or file.path}"
    }
```

#### ******* 进程实体 (ProcessEntity)
```python
# 节点标签: ProcessEntity
Properties:
- process_signature: STRING (进程签名，主要匹配字段)
- process_name: STRING (进程名称，备选匹配字段)
- process_path: STRING (进程路径)
- process_id: INT (进程ID)
- command_line: STRING (命令行)
- user_name: STRING (用户名)
- reuse_count: INT
- risk_score: INT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### ******* URL实体 (UrlEntity)
```python
# 节点标签: UrlEntity
Properties:
- full_url: STRING (完整URL，主要匹配字段)
- url: STRING (URL路径)
- domain: STRING (域名)
- hostname: STRING (主机名)
- path: STRING (路径)
- port: INT (端口)
- scheme: STRING (协议)
- reuse_count: INT
- risk_score: INT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### ******* DNS查询实体 (DnsQueryEntity)
```python
# 节点标签: DnsQueryEntity
Properties:
- query_name: STRING (查询域名，主要匹配字段)
- query_type: STRING (查询类型，辅助匹配字段)
- hostname: STRING (主机名)
- query_class: STRING (查询类)
- opcode: STRING (操作码)
- reuse_count: INT
- risk_score: INT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### ******* HTTP请求实体 (HttpRequestEntity)
```python
# 节点标签: HttpRequestEntity
Properties:
- method: STRING (HTTP方法，主要匹配字段)
- url: STRING (请求URL，主要匹配字段)
- user_agent: STRING (用户代理，主要匹配字段)
- body: STRING (请求体)
- version: STRING (HTTP版本)
- hostname: STRING (主机名)
- reuse_count: INT
- risk_score: INT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

### 3.2 关系类型定义

基于 `app/core/constants.py` 中的 `RelationshipTypes` 类和实际代码实现：

```python
# 已实现的关系类型
class RelationshipTypes:
    HAS_VERDICT = "HAS_VERDICT"            # 告警细节->研判记录
    HAS_EVIDENCE = "HAS_EVIDENCE"          # 告警细节->证据信息
    RELATES_TO = "RELATES_TO"              # 证据信息->风险实体
    GENERATED_BY = "GENERATED_BY"          # 告警细节->设备信息
    BELONGS_TO = "BELONGS_TO"              # 告警细节->告警会话 (未实现)
    CONTAINS = "CONTAINS"                  # 告警会话->告警事件 (未实现)
```

#### 3.2.1 HAS_EVIDENCE 关系 (已实现)
**方向**: AlertDetail -> Evidence
**用途**: 关联告警细节与其证据信息
**属性**:
```python
Properties:
- created_time: TIMESTAMP (关系创建时间)
- is_trigger: BOOLEAN (证据是否为告警触发原因)
- has_payload: BOOLEAN (证据是否包含攻击载荷)
- evidence_count: INT (证据制品数量)
```

**实现位置**: `app/services/evidence_service.py:103-115`

#### 3.2.2 RELATES_TO 关系 (已实现)
**方向**: Evidence -> Entity (各种风险实体)
**用途**: 关联证据信息与从中提取的风险实体
**属性**:
```python
Properties:
- entity_type: STRING (实体类型)
- created_time: TIMESTAMP (关系创建时间)
- extraction_reason: STRING (提取原因，固定为"从证据中自动提取")
- is_new_entity: BOOLEAN (实体是否为新创建)
- entity_context: STRING (实体上下文信息JSON，包含实体快照)
```

**实现位置**: `app/services/evidence_service.py:442-471`

#### 3.2.3 GENERATED_BY 关系 (已实现)
**方向**: AlertDetail -> Device
**用途**: 关联告警细节与生成该告警的设备
**属性**:
```python
Properties:
- created_time: TIMESTAMP (关系创建时间)
- relationship_reason: STRING (关系原因，固定为"告警由此设备生成")
```

**实现位置**: `app/services/device_service.py:48-69`

#### 3.2.4 HAS_VERDICT 关系 (已实现)
**方向**: AlertDetail -> Verdict
**用途**: 关联告警细节与研判记录
**属性**:
```python
Properties:
- created_time: TIMESTAMP (关系创建时间)
```

**实现位置**: `app/services/verdict_service.py:69-98`

## 4. 核心接口设计

### 4.1 数据库连接接口

### 4.1.1 连接管理器

```python
class Neo4jConnection:
    """Neo4j连接管理器"""

    def __init__(self, uri: str, user: str, password: str)
    def get_driver(self) -> Driver
    def close(self)
    def execute_query(self, query: str, parameters: dict = None) -> List[Record]
    def execute_write_transaction(self, query: str, parameters: dict = None) -> List[Record]
    def execute_read_transaction(self, query: str, parameters: dict = None) -> List[Record]

```

### 4.1.2 基础仓储类

```python
class BaseRepository:
    """基础仓储类，提供通用CRUD操作"""

    def __init__(self, connection: Neo4jConnection)
    def create_node(self, label: str, properties: dict) -> str
    def get_node_by_id(self, node_id: str) -> Optional[dict]
    def update_node(self, node_id: str, properties: dict) -> bool
    def delete_node(self, node_id: str) -> bool
    def create_relationship(self, from_id: str, to_id: str, rel_type: str, properties: dict = None) -> bool
    def batch_create_nodes(self, nodes: List[dict]) -> List[str]

```

### 4.2 业务接口设计

### 4.2.1 告警管理接口

```python
class AlertService:
    """告警管理服务"""

    # 数据写入
    def create_alert_detail(self, alert_data: AlertDetailCreate) -> AlertDetailResponse
    def batch_create_alerts(self, alerts: List[AlertDetailCreate]) -> BatchCreateResponse
    def stream_create_alert(self, alert_data: AlertDetailCreate) -> AlertDetailResponse

    # 数据查询
    def get_alert_by_id(self, alert_id: str) -> Optional[AlertDetailResponse]
    def get_alerts_by_device(self, device_id: str) -> List[AlertDetailResponse]
    def get_alerts_by_time_range(self, start_time: datetime, end_time: datetime) -> List[AlertDetailResponse]
    def search_alerts(self, filters: AlertSearchFilters) -> PaginatedResponse[AlertDetailResponse]

    # 状态更新
    def update_alert_status(self, alert_id: str, status: str) -> bool
    def update_alert_fields(self, alert_id: str, update_data: AlertDetailUpdate) -> AlertDetailResponse

    # 关联操作
    def link_alert_to_session(self, alert_id: str, session_id: str) -> bool
    def link_alert_to_entity(self, alert_id: str, entity_id: str, entity_type: str) -> bool

```

### 4.2.2 研判管理接口

```python
class VerdictService:
    """研判管理服务"""

    # 研判记录管理
    def create_verdict(self, verdict_data: VerdictCreate) -> VerdictResponse
    def get_verdicts_by_alert(self, alert_id: str) -> List[VerdictResponse]
    def get_latest_verdict(self, alert_id: str) -> Optional[VerdictResponse]
    def update_verdict(self, verdict_id: str, update_data: VerdictUpdate) -> VerdictResponse

    # 批量研判
    def batch_apply_verdict(self, alert_ids: List[str], verdict_data: VerdictCreate) -> BatchVerdictResponse
    def apply_verdict_to_similar_alerts(self, alert_id: str, verdict_data: VerdictCreate) -> BatchVerdictResponse

```

### 4.2.3 实体管理接口

```python
class EntityService:
    """风险实体管理服务"""

    # 实体创建和去重
    def create_entity(self, entity_data: EntityCreate) -> EntityResponse
    def find_or_create_entity(self, entity_data: EntityCreate) -> tuple[EntityResponse, bool]
    def batch_create_entities(self, entities: List[EntityCreate]) -> BatchCreateResponse

    # 实体查询
    def get_entity_by_id(self, entity_id: str) -> Optional[EntityResponse]
    def get_entities_by_type(self, entity_type: str) -> List[EntityResponse]
    def search_entities(self, search_params: EntitySearchParams) -> PaginatedResponse[EntityResponse]

    # 去重相关功能
    def find_existing_entity(self, match_criteria: dict, entity_type: str) -> Optional[EntityResponse]
    def get_match_criteria(self, entity_data: EntityCreate) -> dict
    def update_entity_on_reuse(self, entity_id: str, entity_data: EntityCreate) -> bool

    # 关联查询
    def get_related_alerts(self, entity_id: str) -> List[AlertDetailResponse]
    def get_entity_relationships(self, entity_id: str) -> List[RelationshipResponse]
    def find_entity_path(self, from_entity_id: str, to_entity_id: str) -> List[PathResponse]

    # 关系管理
    def create_evidence_entity_relationship(self, evidence_id: str, entity_id: str, 
                                          entity_type: str, is_new: bool, 
                                          entity_data: dict) -> bool

    # 统计分析
    def get_entity_reuse_stats(self) -> dict
    def get_deduplication_metrics(self) -> dict

```

### 4.3 REST API接口设计

### 4.3.1 告警相关API

```python
# 告警细节管理
POST   /api/v1/alerts                    # 创建告警细节
POST   /api/v1/alerts/batch              # 批量创建告警细节
GET    /api/v1/alerts/{alert_id}         # 获取告警细节
PUT    /api/v1/alerts/{alert_id}         # 更新告警细节
DELETE /api/v1/alerts/{alert_id}         # 删除告警细节
GET    /api/v1/alerts                    # 查询告警细节列表

# 告警状态管理
PUT    /api/v1/alerts/{alert_id}/status  # 更新告警状态
GET    /api/v1/alerts/status/{status}    # 按状态查询告警

# 告警关联管理
POST   /api/v1/alerts/{alert_id}/entities/{entity_id}  # 关联实体
DELETE /api/v1/alerts/{alert_id}/entities/{entity_id}  # 取消关联实体
GET    /api/v1/alerts/{alert_id}/entities              # 获取关联实体
```

### 4.3.2 研判相关API

```python
# 研判记录管理
POST   /api/v1/verdicts                          # 创建研判记录
GET    /api/v1/verdicts/{verdict_id}             # 获取研判记录
PUT    /api/v1/verdicts/{verdict_id}             # 更新研判记录
DELETE /api/v1/verdicts/{verdict_id}             # 删除研判记录

# 告警研判管理
POST   /api/v1/alerts/{alert_id}/verdicts        # 为告警添加研判
GET    /api/v1/alerts/{alert_id}/verdicts        # 获取告警研判记录
GET    /api/v1/alerts/{alert_id}/verdicts/latest # 获取最新研判
```

### 4.3.3 实体相关API

```python
# 实体管理
POST   /api/v1/entities                    # 创建实体
POST   /api/v1/entities/batch              # 批量创建实体
GET    /api/v1/entities/{entity_id}        # 获取实体
PUT    /api/v1/entities/{entity_id}        # 更新实体
DELETE /api/v1/entities/{entity_id}        # 删除实体
GET    /api/v1/entities                    # 查询实体列表

# 实体关联查询
GET    /api/v1/entities/{entity_id}/alerts      # 获取实体关联告警
GET    /api/v1/entities/{entity_id}/relations   # 获取实体关系
GET    /api/v1/entities/types/{entity_type}     # 按类型查询实体
```

## 5. 核心业务流程详细说明

### 5.1 告警细节节点创建流程

基于 `app/services/alert_service.py` 中的 `create_alert_detail` 方法实现：

```python
def create_alert_detail(self, alert_data: AlertDetailCreate) -> AlertDetailResponse:
    """
    告警细节创建完整流程

    1. 生成唯一标识
    2. 准备节点属性
    3. 创建告警节点
    4. 处理证据信息
    5. 提取设备信息
    6. 建立关联关系
    7. 返回响应
    """
```

#### 5.1.1 详细步骤说明

**步骤1: 生成唯一标识**
```python
# 生成图数据库节点ID
vid = str(uuid4())
current_time = datetime.now()
```

**步骤2: 准备节点属性**
```python
properties = {
    "vid": vid,
    "alert_detail_id": alert_data.alert_detail_id,
    "alert_name": alert_data.alert_name,
    "alert_message": alert_data.alert_message,
    "vulnerabilities": alert_data.vulnerabilities,
    "product_name": alert_data.product_name,
    "time": current_time.isoformat(),
    "confidence_score": alert_data.confidence_score,
    "impact": alert_data.impact.value,
    "risk": alert_data.risk.value,
    "severity": alert_data.severity.value,
    "status": alert_data.status.value,
    "comment": alert_data.comment,
    "raw_data": alert_data.raw_data,
    "created_at": current_time.isoformat(),
    "updated_at": current_time.isoformat()
}
```

**步骤3: 创建告警节点**
```python
# 使用BaseRepository创建AlertDetail节点
node_id = self.repository.create_node("AlertDetail", properties)
```

**步骤4: 处理证据信息**
```python
# 如果存在evidence_artifacts，创建Evidence节点并建立关系
if alert_data.evidence_artifacts:
    evidence_id = self.evidence_service.process_evidence_artifacts(
        vid, alert_data.evidence_artifacts
    )
```

**步骤5: 提取设备信息**
```python
# 从告警数据中提取设备信息，创建或更新Device节点
device_id = self.device_service.extract_and_create_device(alert_data)
if device_id:
    self.device_service.link_alert_to_device(vid, device_id)
```

### 5.2 证据节点生成逻辑和关联机制

基于 `app/services/evidence_service.py` 中的实际实现：

#### 5.2.1 证据节点创建流程

```python
def process_evidence_artifacts(self, alert_id: str, evidence_artifacts: List[EvidenceArtifactsModel]) -> str:
    """
    证据处理完整流程

    1. 创建单个Evidence节点（合并所有evidence_artifacts）
    2. 从所有evidence_artifacts中提取风险实体
    3. 创建或关联风险实体
    4. 建立Evidence->Entity关系
    5. 建立AlertDetail->Evidence关系
    """
```

**步骤1: 创建Evidence节点**
```python
# 生成基于内容的确定性ID
evidence_id = self._generate_evidence_id("combined_evidence", all_evidence_content)

# 合并所有evidence_artifacts信息
properties = {
    "evidence_id": evidence_id,
    "evidence_type": "combined_evidence",
    "evidence_types": evidence_types,  # 所有证据类型列表
    "is_alert_trigger": has_trigger,   # 是否包含触发证据
    "include_payload": has_payload,    # 是否包含攻击载荷
    "evidence_count": len(evidence_artifacts),
    "raw_evidence_data": json.dumps(all_raw_data, ensure_ascii=False)
}

# 创建Evidence节点
node_id = self.repository.create_node("Evidence", properties)
```

**步骤2: 建立AlertDetail->Evidence关系**
```python
# 创建HAS_EVIDENCE关系
relationship_created = self.repository.create_relationship(
    alert_id,
    evidence_id,
    RelationshipTypes.HAS_EVIDENCE,
    {
        "created_time": current_time.isoformat(),
        "is_trigger": has_trigger,
        "has_payload": has_payload,
        "evidence_count": len(evidence_artifacts)
    },
    "AlertDetail",  # from_label
    "Evidence"      # to_label
)
```

#### 5.2.2 风险实体提取和关联流程

**支持的实体类型**:
- HttpRequestEntity (HTTP请求实体)
- HttpResponseEntity (HTTP响应实体)
- DnsQueryEntity (DNS查询实体)
- NetworkEndpointEntity (网络端点实体)
- UrlEntity (URL实体)
- FileEntity (文件实体)
- ProcessEntity (进程实体)

**提取逻辑**:
```python
def _extract_risk_entities(self, evidence: EvidenceArtifactsModel) -> List[Dict[str, Any]]:
    """从单个evidence_artifact中提取所有风险实体"""
    entities = []

    # 根据evidence内容提取不同类型的实体
    if evidence.http_request:
        entities.append(self._extract_http_request_entity(evidence.http_request))
        if evidence.http_request.url:
            entities.append(self._extract_url_entity(evidence.http_request.url))

    if evidence.src_endpoint:
        entities.append(self._extract_network_endpoint_entity(evidence.src_endpoint, "src"))

    # ... 其他实体类型提取

    return entities
```

**实体关联逻辑**:
```python
def _link_evidence_to_entity(self, evidence_id: str, entity_id: str, entity_type: EntityType, entity_data: dict, is_new: bool):
    """创建Evidence->Entity的RELATES_TO关系"""
    rel_properties = {
        "entity_type": entity_type.value,
        "created_time": current_time.isoformat(),
        "extraction_reason": "从证据中自动提取",
        "is_new_entity": is_new,
        "entity_context": json.dumps({
            "entity_name": entity_data.get("name", ""),
            "risk_score": entity_data.get("risk_score", 0),
            "properties_snapshot": entity_data.get("properties", {}),
            "extraction_time": current_time.isoformat()
        }, ensure_ascii=False)
    }

    # 创建RELATES_TO关系
    self.repository.create_relationship(
        evidence_id, entity_id, RelationshipTypes.RELATES_TO,
        rel_properties, "Evidence", entity_label
    )
```

### 5.3 风险实体去重、创建的完整流程

基于 `app/services/entity_service.py` 中的 `find_or_create_entity` 方法：

#### 5.3.1 实体去重流程

```python
def find_or_create_entity(self, entity_data: EntityCreate) -> tuple[EntityResponse, bool]:
    """
    实体去重核心流程

    1. 查找现有匹配实体
    2. 如果找到：更新复用信息，返回现有实体
    3. 如果未找到：创建新实体
    4. 返回(实体响应, 是否新创建)
    """

    # 先尝试查找现有实体
    existing_entity = self._find_existing_entity(entity_data)

    if existing_entity:
        # 找到现有实体，更新时间和风险分数
        self._update_entity_on_reuse(existing_entity.entity_id, entity_data)
        return existing_entity, False
    else:
        # 创建新实体
        new_entity = self.create_entity(entity_data)
        return new_entity, True
```

**当前实现状态**: 框架已实现，`_find_existing_entity` 方法需要完善具体匹配逻辑

#### 5.3.2 实体创建流程

```python
def create_entity(self, entity_data: EntityCreate) -> EntityResponse:
    """创建新实体节点"""

    # 生成唯一ID
    entity_id = str(uuid4())
    current_time = datetime.now()

    # 准备节点属性
    properties = {
        "entity_id": entity_id,
        "name": entity_data.name,
        "risk_score": entity_data.risk_score,
        "description": entity_data.description,
        "reuse_count": 1,  # 初始复用次数为1
        "created_at": current_time.isoformat(),
        "updated_at": current_time.isoformat()
    }

    # 添加实体特定属性
    properties.update(entity_data.properties)

    # 为ProcessEntity添加特殊字段
    if entity_data.entity_type.value == "ProcessEntity":
        if "process_name" in entity_data.properties and "command_line" in entity_data.properties:
            process_signature = f"{entity_data.properties['process_name']}|{entity_data.properties['command_line']}"
            properties["process_signature"] = hashlib.md5(process_signature.encode()).hexdigest()

    # 创建节点，使用实体类型作为标签
    self.repository.create_node(entity_data.entity_type.value, properties)

    return EntityResponse(...)
```

### 5.4 设备信息提取和关联逻辑

基于 `app/services/device_service.py` 中的实现：

#### 5.4.1 设备信息提取

```python
def extract_and_create_device(self, alert_data: AlertDetailCreate) -> Optional[str]:
    """
    从告警数据中提取设备信息

    1. 从告警数据中提取设备相关字段
    2. 生成设备ID
    3. 检查设备是否已存在
    4. 如果存在：更新告警计数
    5. 如果不存在：创建新设备节点
    """

    device_info = self._extract_device_info(alert_data)
    if not device_info:
        return None

    device_id = self._generate_device_id(device_info)
    existing_device = self._get_device_by_id(device_id)

    if existing_device:
        self._increment_alert_count(device_id)
        return device_id
    else:
        return self._create_device_node(device_id, device_info)
```

#### 5.4.2 设备关联

```python
def link_alert_to_device(self, alert_id: str, device_id: str):
    """创建AlertDetail->Device的GENERATED_BY关系"""

    relationship_created = self.repository.create_relationship(
        alert_id, device_id, RelationshipTypes.GENERATED_BY,
        {
            "created_time": current_time.isoformat(),
            "relationship_reason": "告警由此设备生成"
        },
        "AlertDetail", "Device"
    )
```

## 6. 风险实体去重机制设计

### 6.1 当前实现状态

**已实现部分**:
- 实体去重框架 (`find_or_create_entity` 方法)
- 实体创建和更新逻辑
- ProcessEntity 的 process_signature 生成

**待实现部分**:
- 具体的实体匹配算法 (`_find_existing_entity` 方法)
- 6种实体类型的匹配策略实现

### 6.1.1 设计原则

- **共性优先**: 风险实体内部存放多类风险告警的共性字段
- **智能匹配**: 针对不同实体类型设计专门的匹配规则
- **特征记录**: 在关系中记录证据上下文和实体特征
- **时间维护**: 保持创建时间、更新时间和复用统计

### 6.2 实体匹配策略

### 6.2.1 网络端点实体 (NetworkEndpointEntity)

**匹配字段**: `ip_address` + `port`

```python
# 匹配逻辑
if "ip_address" in properties and "port" in properties:
    criteria = {
        "ip_address": properties["ip_address"],
        "port": properties["port"]
    }
```

**实体属性**:
```python
Properties:
- ip_address: STRING (主要匹配字段)
- port: INT (辅助匹配字段)
- hostname: STRING
- mac: STRING
- domain: STRING
- endpoint_type: STRING (src/dst)
- location: OBJECT (地理位置信息)
- autonomous_system: OBJECT (AS信息)
- operating_system: OBJECT (操作系统信息)
- reuse_count: INT (复用次数统计)
- risk_score: INT (风险分数，取最高值)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

### 6.2.2 文件实体 (FileEntity)

**匹配字段**: 优先 `file_hash`，次选 `filename`

```python
# 匹配逻辑 - 优先级1: 文件哈希
if "file_hash" in properties and properties["file_hash"]:
    criteria = {"file_hash": properties["file_hash"]}

# 匹配逻辑 - 优先级2: 文件名
elif "filename" in properties and properties["filename"]:
    criteria = {"filename": properties["filename"]}
```

**设计原理**: 
- **文件哈希**是最可靠的匹配方式，相同哈希必然是同一文件
- **文件名**作为备选，因为相同的恶意文件（如malware.exe）可能出现在不同路径
- **文件路径**不作为匹配字段，因为同一文件可能被复制到不同位置

**实体属性**:
```python
Properties:
- file_hash: STRING (主要匹配字段)
- filename: STRING (备选匹配字段)
- file_path: STRING (文件路径，不用于匹配)
- file_size: INT
- file_extension: STRING
- file_type: STRING
- mime_type: STRING
- created_time: TIMESTAMP (文件创建时间)
- modified_time: TIMESTAMP (文件修改时间)
- accessed_time: TIMESTAMP (文件访问时间)
- hash_md5: STRING
- hash_sha1: STRING
- hash_sha256: STRING
- reuse_count: INT
- risk_score: INT
```

### 6.2.3 进程实体 (ProcessEntity)

**匹配字段**: `process_signature` (进程名+命令行的MD5哈希)

```python
# 匹配逻辑
if "process_name" in properties and "command_line" in properties:
    process_signature = f"{properties['process_name']}|{properties['command_line']}"
    process_hash = hashlib.md5(process_signature.encode()).hexdigest()
    criteria = {"process_signature": process_hash}
elif "process_name" in properties:
    criteria = {"process_name": properties["process_name"]}
```

**实体属性**:
```python
Properties:
- process_signature: STRING (主要匹配字段，自动生成)
- process_name: STRING (备选匹配字段)
- process_path: STRING
- process_id: INT
- command_line: STRING
- working_directory: STRING
- user_name: STRING
- user_type: STRING
- process_created_time: TIMESTAMP
- environment_variables: JSON
- reuse_count: INT
- risk_score: INT
```

### 6.2.4 URL实体 (UrlEntity)

**匹配字段**: `full_url`

```python
# 匹配逻辑
if "full_url" in properties:
    criteria = {"full_url": properties["full_url"]}
```

**实体属性**:
```python
Properties:
- full_url: STRING (主要匹配字段)
- url: STRING (保留字段)
- domain: STRING
- subdomain: STRING
- hostname: STRING
- path: STRING
- port: INT
- query_string: STRING
- scheme: STRING
- categories: STRING (逗号分隔)
- reuse_count: INT
- risk_score: INT
```

### 6.2.5 DNS查询实体 (DnsQueryEntity)

**匹配字段**: `query_name` + `query_type`

```python
# 匹配逻辑
criteria = {}
if "query_name" in properties:
    criteria["query_name"] = properties["query_name"]
if "query_type" in properties:
    criteria["query_type"] = properties["query_type"]
```

**实体属性**:
```python
Properties:
- query_name: STRING (主要匹配字段)
- query_type: STRING (辅助匹配字段)
- hostname: STRING (保留字段)
- query_class: STRING
- opcode: STRING
- opcode_id: INT
- packet_uid: INT
- reuse_count: INT
- risk_score: INT
```

### 6.2.6 HTTP请求实体 (HttpRequestEntity)

**匹配字段**: `method` + `url` + `user_agent`

```python
# 匹配逻辑
criteria = {}
if "method" in properties:
    criteria["method"] = properties["method"]
if "url" in properties:
    criteria["url"] = properties["url"]
if "user_agent" in properties:
    criteria["user_agent"] = properties["user_agent"]
```

**实体属性**:
```python
Properties:
- method: STRING (主要匹配字段)
- url: STRING (主要匹配字段)
- user_agent: STRING (主要匹配字段)
- body: STRING
- body_length: INT
- length: INT
- referer: STRING
- version: STRING
- hostname: STRING
- reuse_count: INT
- risk_score: INT
```

### 6.3 实体生成流程

### 6.3.1 查找或创建流程

```python
def find_or_create_entity(entity_data: EntityCreate) -> tuple[EntityResponse, bool]:
    """
    实体去重核心流程
    
    Returns:
        tuple: (实体响应, 是否新创建)
    """
    # 1. 根据实体类型获取匹配条件
    match_criteria = get_match_criteria(entity_data)
    
    # 2. 查找现有匹配实体
    existing_entity = find_existing_entity(match_criteria, entity_data.entity_type)
    
    if existing_entity:
        # 3a. 找到匹配实体 - 更新复用信息
        update_entity_on_reuse(existing_entity.entity_id, entity_data)
        return existing_entity, False
    else:
        # 3b. 未找到匹配 - 创建新实体
        new_entity = create_entity(entity_data)
        return new_entity, True
```

### 6.3.2 实体复用更新逻辑

当实体被复用时，系统会更新以下字段：

```python
def update_entity_on_reuse(entity_id: str, entity_data: EntityCreate):
    """实体复用时的更新逻辑"""
    
    update_props = {
        "updated_at": current_time.isoformat(),
        "reuse_count": "COALESCE(e.reuse_count, 0) + 1"
    }
    
    # 风险分数取最高值
    if entity_data.risk_score > 0:
        update_props["risk_score"] = f"""
        CASE WHEN e.risk_score < {entity_data.risk_score} 
             THEN {entity_data.risk_score} 
             ELSE e.risk_score END
        """
```

### 6.4 关系特征记录

### 6.4.1 证据-实体关系增强

在 `Evidence` → `Entity` 的 `RELATES_TO` 关系中记录详细的上下文信息：


### 6.4.2 关系属性说明

- `is_new_entity`: 布尔值，标记该关系建立时实体是否为新创建
- `entity_context`: JSON字符串，包含实体在关联时的快照信息
- `extraction_reason`: 字符串，说明实体提取的原因
- `properties_snapshot`: 实体属性快照，用于溯源分析

### 6.5 性能优化策略

### 6.5.1 索引设计

为提高匹配效率，在关键匹配字段上建立索引：

```cypher
-- 网络端点实体索引
CREATE INDEX idx_network_endpoint_ip_port IF NOT EXISTS
FOR (n:NetworkEndpointEntity)
ON (n.ip_address, n.port);

-- 文件实体索引
CREATE INDEX idx_file_hash IF NOT EXISTS
FOR (n:FileEntity)
ON (n.file_hash);

CREATE INDEX idx_file_path IF NOT EXISTS
FOR (n:FileEntity)
ON (n.file_path);

-- 进程实体索引
CREATE INDEX idx_process_signature IF NOT EXISTS
FOR (n:ProcessEntity)
ON (n.process_signature);

-- URL实体索引
CREATE INDEX idx_url_full IF NOT EXISTS
FOR (n:UrlEntity)
ON (n.full_url);

-- DNS查询实体索引
CREATE INDEX idx_dns_query IF NOT EXISTS
FOR (n:DnsQueryEntity)
ON (n.query_name, n.query_type);
```

### 6.5.2 匹配优化

- **分层匹配**: 优先使用高区分度字段（如哈希值）进行匹配
- **早期退出**: 匹配条件为空时直接创建新实体
- **批量处理**: 支持批量实体去重处理

## 7. 当前实现状态总结

### 7.1 已完成功能

#### 7.1.1 核心节点类型 ✅
- **AlertDetail**: 完整实现，包含所有必要属性和验证
- **Verdict**: 完整实现，支持AI和人工研判
- **Evidence**: 完整实现，支持多种证据类型合并
- **Device**: 完整实现，支持设备信息提取和去重

#### 7.1.2 关系类型 ✅
- **HAS_EVIDENCE**: AlertDetail -> Evidence，包含触发和载荷信息
- **RELATES_TO**: Evidence -> Entity，包含实体上下文快照
- **GENERATED_BY**: AlertDetail -> Device，包含生成原因
- **HAS_VERDICT**: AlertDetail -> Verdict，支持多次研判

#### 7.1.3 业务流程 ✅
- **告警创建**: 完整的告警细节创建流程
- **证据处理**: 自动提取和关联风险实体
- **设备关联**: 自动提取设备信息并建立关联
- **研判管理**: 支持告警研判记录的创建和查询

#### 7.1.4 实体管理 ✅
- **实体创建**: 支持7种实体类型的创建
- **去重框架**: 实体去重的基础框架已实现
- **关系管理**: 实体间关系的创建和查询

### 7.2 部分实现功能

#### 7.2.1 实体去重机制 🔄
- **框架完成**: `find_or_create_entity` 方法框架已实现
- **待完善**: 具体的匹配算法需要实现
- **已实现**: ProcessEntity 的 process_signature 生成

#### 7.2.2 风险实体类型 🔄
- **基础实现**: 7种实体类型的提取逻辑已实现
- **待完善**: 实体属性的完整性和匹配策略

### 7.3 未实现功能

#### 7.3.1 高级业务逻辑 ❌
- **告警会话**: 告警会话的创建和管理
- **告警事件**: 告警事件的聚合和管理
- **图谱分析**: 路径分析、异常检测等高级功能

#### 7.3.2 系统优化 ❌
- **性能优化**: 数据库索引策略
- **缓存机制**: 热点数据缓存
- **批量优化**: 真正的批量事务处理

### 7.4 代码质量状态

#### 7.4.1 优势 ✅
- **清晰架构**: 分层架构设计良好
- **类型安全**: 完整的Pydantic模型定义
- **错误处理**: 基础的异常处理机制
- **日志记录**: 结构化日志和业务操作追踪

#### 7.4.2 需要改进 ⚠️
- **测试覆盖**: 缺少单元测试和集成测试
- **文档同步**: 部分代码实现与设计文档不一致
- **性能监控**: 缺少详细的性能指标

### 7.5 下一步开发重点

1. **完善实体去重**: 实现6种实体类型的具体匹配算法
2. **添加测试**: 建立完整的测试体系
3. **性能优化**: 添加数据库索引和查询优化
4. **告警会话**: 实现告警会话管理功能

---

**文档更新日期**: 2025-06-09
**基于代码版本**: 当前主分支
**更新范围**: 关系模型、业务流程、数据模型实际状态