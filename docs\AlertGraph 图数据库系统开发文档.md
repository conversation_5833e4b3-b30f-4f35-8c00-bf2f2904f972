# AlertGraph 图数据库系统开发文档

## 1. 项目概述

### 1.1 项目简介

AlertGraph是一个基于Neo4j图数据库的安全告警知识图谱系统，用于存储、管理和查询安全告警相关数据，支持三层告警逻辑（告警细节、告警会话、告警事件）的知识图谱构建。

### 1.2 技术栈

- **Python**: 3.12
- **Web框架**: FastAPI 0.115.12
- **图数据库**: Neo4j 5.26.7-community

### 1.3 核心功能

- 告警数据的批量和流式写入
- 多层级告警关系构建
- 风险实体关联分析
- **智能实体去重机制**
- 研判记录管理
- 威胁情报集成
- RESTful API接口服务

## 2. 系统架构设计

### 2.1 核心组件设计

### 2.1.1 数据库连接层

负责Neo4j数据库连接管理、连接池维护、事务处理等基础功能。

### 2.1.2 数据模型层

定义图数据库中的节点和关系模型，包括告警细节、研判记录、风险实体等。

### 2.1.3 数据访问层（Repository Pattern）

封装对Neo4j的CRUD操作，提供面向对象的数据访问接口。

### 2.1.4 业务逻辑层

实现核心业务逻辑，包括告警关联、研判处理、图谱构建等。

### 2.1.5 API层

提供RESTful API接口，支持前端和外部系统调用。

## 3. 数据模型设计

### 3.1 节点类型定义

### 3.1.1 告警细节节点 (AlertDetail)

```python
# 节点标签: AlertDetail
# 属性定义基于OCSF标准
Properties:
- vid: STRING (唯一ID)
- alert_detail_id: STRING (告警细节ID)
- alert_name: STRING (告警名称)
- alert_message: STRING (告警描述)
- vulnerabilities: STRING (漏洞描述)
- product_name: STRING (设备名称)
- time: TIMESTAMP (告警时间)
- confidence_score: INT (置信度分数 0-100)
- impact: STRING (影响等级: Critical/High/Medium/Low/Unknown)
- risk: STRING (风险等级: Critical/High/Medium/Low/Unknown)
- severity: STRING (严重性: Fatal/Critical/High/Medium/Low/Unknown)
- status: STRING (处理状态: New/InProgress/Suppressed/Resolved/Archived/Unknown)
- comment: STRING (评论)
- raw_data: STRING (原始推送数据)
```

### 3.1.2 研判记录节点 (Verdict)

```python
# 节点标签: Verdict
Properties:
- verdict_id: STRING (唯一ID)
- type_id: INT (研判类型: 1-AI, 2-人工)
- label: INT (研判标签: 1-误报, 2-确认攻击, 3-可疑, 4-数据不足)
- reason: STRING (研判理由)
- commiter: STRING (研判提交者)
- time: TIMESTAMP (研判时间)
- comment: STRING (研判评论)
```

### 3.1.3 证据信息节点 (Evidence)

```python
# 节点标签: Evidence
Properties:
- evidence_id: STRING (唯一ID)
- evidence_type: STRING (证据类型)
- raw_evidence_data: STRING (原始证据数据JSON)
- created_time: TIMESTAMP (创建时间)
```

### 3.1.4 设备信息节点 (Device)

```python
# 节点标签: Device
Properties:
- device_id: STRING (唯一ID)
- uuid: STRING (设备UUID)
- org: STRING (组织)
- ip: STRING (主要IP)
- hostname: STRING (主机名)
- mac: STRING (MAC地址)
- os_name: STRING (系统名称)
- count: INT (告警数量统计)
```

### 3.1.5 风险实体节点

系统支持多种风险实体类型，每种实体都有专门的匹配和去重机制：

#### ******* 网络端点实体 (NetworkEndpointEntity)
```python
# 节点标签: NetworkEndpointEntity
Properties:
- ip_address: STRING (IP地址，主要匹配字段)
- port: INT (端口号，辅助匹配字段)
- hostname: STRING (主机名)
- mac: STRING (MAC地址)
- domain: STRING (域名)
- endpoint_type: STRING (端点类型: src/dst)
- reuse_count: INT (复用次数)
- risk_score: INT (风险分数)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### ******* 文件实体 (FileEntity)
```python
# 节点标签: FileEntity  
Properties:
- file_hash: STRING (文件哈希，主要匹配字段)
- filename: STRING (文件名，备选匹配字段)
- file_path: STRING (文件路径)
- file_size: INT (文件大小)
- file_type: STRING (文件类型)
- mime_type: STRING (MIME类型)
- hash_md5: STRING (MD5哈希)
- hash_sha1: STRING (SHA1哈希)
- hash_sha256: STRING (SHA256哈希)
- reuse_count: INT
- risk_score: INT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### ******* 进程实体 (ProcessEntity)
```python
# 节点标签: ProcessEntity
Properties:
- process_signature: STRING (进程签名，主要匹配字段)
- process_name: STRING (进程名称，备选匹配字段)
- process_path: STRING (进程路径)
- process_id: INT (进程ID)
- command_line: STRING (命令行)
- user_name: STRING (用户名)
- reuse_count: INT
- risk_score: INT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### ******* URL实体 (UrlEntity)
```python
# 节点标签: UrlEntity
Properties:
- full_url: STRING (完整URL，主要匹配字段)
- url: STRING (URL路径)
- domain: STRING (域名)
- hostname: STRING (主机名)
- path: STRING (路径)
- port: INT (端口)
- scheme: STRING (协议)
- reuse_count: INT
- risk_score: INT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### ******* DNS查询实体 (DnsQueryEntity)
```python
# 节点标签: DnsQueryEntity
Properties:
- query_name: STRING (查询域名，主要匹配字段)
- query_type: STRING (查询类型，辅助匹配字段)
- hostname: STRING (主机名)
- query_class: STRING (查询类)
- opcode: STRING (操作码)
- reuse_count: INT
- risk_score: INT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

#### ******* HTTP请求实体 (HttpRequestEntity)
```python
# 节点标签: HttpRequestEntity
Properties:
- method: STRING (HTTP方法，主要匹配字段)
- url: STRING (请求URL，主要匹配字段)
- user_agent: STRING (用户代理，主要匹配字段)
- body: STRING (请求体)
- version: STRING (HTTP版本)
- hostname: STRING (主机名)
- reuse_count: INT
- risk_score: INT
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

### 3.2 关系类型定义

```python
# 关系定义
RELATIONSHIPS = {
    "HAS_VERDICT": "告警细节->研判记录",
    "HAS_EVIDENCE": "告警细节->证据信息",
    "RELATES_TO": "告警细节->风险实体",
    "GENERATED_BY": "告警细节->设备信息",
    "BELONGS_TO": "告警细节->告警会话",
    "CONTAINS": "告警会话->告警事件"
}
```

## 4. 核心接口设计

### 4.1 数据库连接接口

### 4.1.1 连接管理器

```python
class Neo4jConnection:
    """Neo4j连接管理器"""

    def __init__(self, uri: str, user: str, password: str)
    def get_driver(self) -> Driver
    def close(self)
    def execute_query(self, query: str, parameters: dict = None) -> List[Record]
    def execute_write_transaction(self, query: str, parameters: dict = None) -> List[Record]
    def execute_read_transaction(self, query: str, parameters: dict = None) -> List[Record]

```

### 4.1.2 基础仓储类

```python
class BaseRepository:
    """基础仓储类，提供通用CRUD操作"""

    def __init__(self, connection: Neo4jConnection)
    def create_node(self, label: str, properties: dict) -> str
    def get_node_by_id(self, node_id: str) -> Optional[dict]
    def update_node(self, node_id: str, properties: dict) -> bool
    def delete_node(self, node_id: str) -> bool
    def create_relationship(self, from_id: str, to_id: str, rel_type: str, properties: dict = None) -> bool
    def batch_create_nodes(self, nodes: List[dict]) -> List[str]

```

### 4.2 业务接口设计

### 4.2.1 告警管理接口

```python
class AlertService:
    """告警管理服务"""

    # 数据写入
    def create_alert_detail(self, alert_data: AlertDetailCreate) -> AlertDetailResponse
    def batch_create_alerts(self, alerts: List[AlertDetailCreate]) -> BatchCreateResponse
    def stream_create_alert(self, alert_data: AlertDetailCreate) -> AlertDetailResponse

    # 数据查询
    def get_alert_by_id(self, alert_id: str) -> Optional[AlertDetailResponse]
    def get_alerts_by_device(self, device_id: str) -> List[AlertDetailResponse]
    def get_alerts_by_time_range(self, start_time: datetime, end_time: datetime) -> List[AlertDetailResponse]
    def search_alerts(self, filters: AlertSearchFilters) -> PaginatedResponse[AlertDetailResponse]

    # 状态更新
    def update_alert_status(self, alert_id: str, status: str) -> bool
    def update_alert_fields(self, alert_id: str, update_data: AlertDetailUpdate) -> AlertDetailResponse

    # 关联操作
    def link_alert_to_session(self, alert_id: str, session_id: str) -> bool
    def link_alert_to_entity(self, alert_id: str, entity_id: str, entity_type: str) -> bool

```

### 4.2.2 研判管理接口

```python
class VerdictService:
    """研判管理服务"""

    # 研判记录管理
    def create_verdict(self, verdict_data: VerdictCreate) -> VerdictResponse
    def get_verdicts_by_alert(self, alert_id: str) -> List[VerdictResponse]
    def get_latest_verdict(self, alert_id: str) -> Optional[VerdictResponse]
    def update_verdict(self, verdict_id: str, update_data: VerdictUpdate) -> VerdictResponse

    # 批量研判
    def batch_apply_verdict(self, alert_ids: List[str], verdict_data: VerdictCreate) -> BatchVerdictResponse
    def apply_verdict_to_similar_alerts(self, alert_id: str, verdict_data: VerdictCreate) -> BatchVerdictResponse

```

### 4.2.3 实体管理接口

```python
class EntityService:
    """风险实体管理服务"""

    # 实体创建和去重
    def create_entity(self, entity_data: EntityCreate) -> EntityResponse
    def find_or_create_entity(self, entity_data: EntityCreate) -> tuple[EntityResponse, bool]
    def batch_create_entities(self, entities: List[EntityCreate]) -> BatchCreateResponse

    # 实体查询
    def get_entity_by_id(self, entity_id: str) -> Optional[EntityResponse]
    def get_entities_by_type(self, entity_type: str) -> List[EntityResponse]
    def search_entities(self, search_params: EntitySearchParams) -> PaginatedResponse[EntityResponse]

    # 去重相关功能
    def find_existing_entity(self, match_criteria: dict, entity_type: str) -> Optional[EntityResponse]
    def get_match_criteria(self, entity_data: EntityCreate) -> dict
    def update_entity_on_reuse(self, entity_id: str, entity_data: EntityCreate) -> bool

    # 关联查询
    def get_related_alerts(self, entity_id: str) -> List[AlertDetailResponse]
    def get_entity_relationships(self, entity_id: str) -> List[RelationshipResponse]
    def find_entity_path(self, from_entity_id: str, to_entity_id: str) -> List[PathResponse]

    # 关系管理
    def create_evidence_entity_relationship(self, evidence_id: str, entity_id: str, 
                                          entity_type: str, is_new: bool, 
                                          entity_data: dict) -> bool

    # 统计分析
    def get_entity_reuse_stats(self) -> dict
    def get_deduplication_metrics(self) -> dict

```

### 4.3 REST API接口设计

### 4.3.1 告警相关API

```python
# 告警细节管理
POST   /api/v1/alerts                    # 创建告警细节
POST   /api/v1/alerts/batch              # 批量创建告警细节
GET    /api/v1/alerts/{alert_id}         # 获取告警细节
PUT    /api/v1/alerts/{alert_id}         # 更新告警细节
DELETE /api/v1/alerts/{alert_id}         # 删除告警细节
GET    /api/v1/alerts                    # 查询告警细节列表

# 告警状态管理
PUT    /api/v1/alerts/{alert_id}/status  # 更新告警状态
GET    /api/v1/alerts/status/{status}    # 按状态查询告警

# 告警关联管理
POST   /api/v1/alerts/{alert_id}/entities/{entity_id}  # 关联实体
DELETE /api/v1/alerts/{alert_id}/entities/{entity_id}  # 取消关联实体
GET    /api/v1/alerts/{alert_id}/entities              # 获取关联实体
```

### 4.3.2 研判相关API

```python
# 研判记录管理
POST   /api/v1/verdicts                          # 创建研判记录
GET    /api/v1/verdicts/{verdict_id}             # 获取研判记录
PUT    /api/v1/verdicts/{verdict_id}             # 更新研判记录
DELETE /api/v1/verdicts/{verdict_id}             # 删除研判记录

# 告警研判管理
POST   /api/v1/alerts/{alert_id}/verdicts        # 为告警添加研判
GET    /api/v1/alerts/{alert_id}/verdicts        # 获取告警研判记录
GET    /api/v1/alerts/{alert_id}/verdicts/latest # 获取最新研判
```

### 4.3.3 实体相关API

```python
# 实体管理
POST   /api/v1/entities                    # 创建实体
POST   /api/v1/entities/batch              # 批量创建实体
GET    /api/v1/entities/{entity_id}        # 获取实体
PUT    /api/v1/entities/{entity_id}        # 更新实体
DELETE /api/v1/entities/{entity_id}        # 删除实体
GET    /api/v1/entities                    # 查询实体列表

# 实体关联查询
GET    /api/v1/entities/{entity_id}/alerts      # 获取实体关联告警
GET    /api/v1/entities/{entity_id}/relations   # 获取实体关系
GET    /api/v1/entities/types/{entity_type}     # 按类型查询实体
```

## 5. 数据处理流程设计

### 5.1 批量数据写入流程

```python
class BatchProcessor:
    """批量数据处理器"""

    def process_historical_alerts(self, file_path: str) -> BatchProcessResult:
        """处理历史告警数据"""
        # 1. 数据读取和验证
        # 2. 数据清洗和标准化
        # 3. 批量创建节点和关系
        # 4. 建立实体关联
        # 5. 返回处理结果
        pass

    def batch_create_with_relationships(self, alert_batch: List[AlertDetailCreate]) -> BatchCreateResponse:
        """批量创建告警及其关系"""
        # 1. 事务管理
        # 2. 节点批量创建
        # 3. 关系批量创建
        # 4. 错误处理和回滚
        pass

```

### 5.2 流式数据写入流程

```python
class StreamProcessor:
    """流式数据处理器"""

    def process_realtime_alert(self, alert_data: AlertDetailCreate) -> AlertDetailResponse:
        """处理实时告警数据"""
        # 1. 数据验证
        # 2. 重复性检查
        # 3. 实体提取和关联
        # 4. 图谱更新
        # 5. 告警会话归并
        pass

    def auto_associate_entities(self, alert_id: str) -> List[str]:
        """自动关联实体"""
        # 1. 解析告警内容
        # 2. 提取风险实体
        # 3. 查找或创建实体节点
        # 4. 建立关联关系
        pass
```

## 6. 风险实体去重机制设计

### 6.1 设计目标

风险实体去重机制旨在解决重复实体创建的问题，通过智能匹配算法识别相同的风险实体，实现实体复用，提高图数据库的存储效率和关联分析准确性。

### 6.1.1 核心原则

- **共性优先**: 风险实体内部存放多类风险告警的共性字段
- **智能匹配**: 针对不同实体类型设计专门的匹配规则
- **特征记录**: 在关系中记录证据上下文和实体特征
- **时间维护**: 保持创建时间、更新时间和复用统计

### 6.2 实体匹配策略

### 6.2.1 网络端点实体 (NetworkEndpointEntity)

**匹配字段**: `ip_address` + `port`

```python
# 匹配逻辑
if "ip_address" in properties and "port" in properties:
    criteria = {
        "ip_address": properties["ip_address"],
        "port": properties["port"]
    }
```

**实体属性**:
```python
Properties:
- ip_address: STRING (主要匹配字段)
- port: INT (辅助匹配字段)
- hostname: STRING
- mac: STRING
- domain: STRING
- endpoint_type: STRING (src/dst)
- location: OBJECT (地理位置信息)
- autonomous_system: OBJECT (AS信息)
- operating_system: OBJECT (操作系统信息)
- reuse_count: INT (复用次数统计)
- risk_score: INT (风险分数，取最高值)
- created_at: TIMESTAMP
- updated_at: TIMESTAMP
```

### 6.2.2 文件实体 (FileEntity)

**匹配字段**: 优先 `file_hash`，次选 `filename`

```python
# 匹配逻辑 - 优先级1: 文件哈希
if "file_hash" in properties and properties["file_hash"]:
    criteria = {"file_hash": properties["file_hash"]}

# 匹配逻辑 - 优先级2: 文件名
elif "filename" in properties and properties["filename"]:
    criteria = {"filename": properties["filename"]}
```

**设计原理**: 
- **文件哈希**是最可靠的匹配方式，相同哈希必然是同一文件
- **文件名**作为备选，因为相同的恶意文件（如malware.exe）可能出现在不同路径
- **文件路径**不作为匹配字段，因为同一文件可能被复制到不同位置

**实体属性**:
```python
Properties:
- file_hash: STRING (主要匹配字段)
- filename: STRING (备选匹配字段)
- file_path: STRING (文件路径，不用于匹配)
- file_size: INT
- file_extension: STRING
- file_type: STRING
- mime_type: STRING
- created_time: TIMESTAMP (文件创建时间)
- modified_time: TIMESTAMP (文件修改时间)
- accessed_time: TIMESTAMP (文件访问时间)
- hash_md5: STRING
- hash_sha1: STRING
- hash_sha256: STRING
- reuse_count: INT
- risk_score: INT
```

### 6.2.3 进程实体 (ProcessEntity)

**匹配字段**: `process_signature` (进程名+命令行的MD5哈希)

```python
# 匹配逻辑
if "process_name" in properties and "command_line" in properties:
    process_signature = f"{properties['process_name']}|{properties['command_line']}"
    process_hash = hashlib.md5(process_signature.encode()).hexdigest()
    criteria = {"process_signature": process_hash}
elif "process_name" in properties:
    criteria = {"process_name": properties["process_name"]}
```

**实体属性**:
```python
Properties:
- process_signature: STRING (主要匹配字段，自动生成)
- process_name: STRING (备选匹配字段)
- process_path: STRING
- process_id: INT
- command_line: STRING
- working_directory: STRING
- user_name: STRING
- user_type: STRING
- process_created_time: TIMESTAMP
- environment_variables: JSON
- reuse_count: INT
- risk_score: INT
```

### 6.2.4 URL实体 (UrlEntity)

**匹配字段**: `full_url`

```python
# 匹配逻辑
if "full_url" in properties:
    criteria = {"full_url": properties["full_url"]}
```

**实体属性**:
```python
Properties:
- full_url: STRING (主要匹配字段)
- url: STRING (保留字段)
- domain: STRING
- subdomain: STRING
- hostname: STRING
- path: STRING
- port: INT
- query_string: STRING
- scheme: STRING
- categories: STRING (逗号分隔)
- reuse_count: INT
- risk_score: INT
```

### 6.2.5 DNS查询实体 (DnsQueryEntity)

**匹配字段**: `query_name` + `query_type`

```python
# 匹配逻辑
criteria = {}
if "query_name" in properties:
    criteria["query_name"] = properties["query_name"]
if "query_type" in properties:
    criteria["query_type"] = properties["query_type"]
```

**实体属性**:
```python
Properties:
- query_name: STRING (主要匹配字段)
- query_type: STRING (辅助匹配字段)
- hostname: STRING (保留字段)
- query_class: STRING
- opcode: STRING
- opcode_id: INT
- packet_uid: INT
- reuse_count: INT
- risk_score: INT
```

### 6.2.6 HTTP请求实体 (HttpRequestEntity)

**匹配字段**: `method` + `url` + `user_agent`

```python
# 匹配逻辑
criteria = {}
if "method" in properties:
    criteria["method"] = properties["method"]
if "url" in properties:
    criteria["url"] = properties["url"]
if "user_agent" in properties:
    criteria["user_agent"] = properties["user_agent"]
```

**实体属性**:
```python
Properties:
- method: STRING (主要匹配字段)
- url: STRING (主要匹配字段)
- user_agent: STRING (主要匹配字段)
- body: STRING
- body_length: INT
- length: INT
- referer: STRING
- version: STRING
- hostname: STRING
- reuse_count: INT
- risk_score: INT
```

### 6.3 实体生成流程

### 6.3.1 查找或创建流程

```python
def find_or_create_entity(entity_data: EntityCreate) -> tuple[EntityResponse, bool]:
    """
    实体去重核心流程
    
    Returns:
        tuple: (实体响应, 是否新创建)
    """
    # 1. 根据实体类型获取匹配条件
    match_criteria = get_match_criteria(entity_data)
    
    # 2. 查找现有匹配实体
    existing_entity = find_existing_entity(match_criteria, entity_data.entity_type)
    
    if existing_entity:
        # 3a. 找到匹配实体 - 更新复用信息
        update_entity_on_reuse(existing_entity.entity_id, entity_data)
        return existing_entity, False
    else:
        # 3b. 未找到匹配 - 创建新实体
        new_entity = create_entity(entity_data)
        return new_entity, True
```

### 6.3.2 实体复用更新逻辑

当实体被复用时，系统会更新以下字段：

```python
def update_entity_on_reuse(entity_id: str, entity_data: EntityCreate):
    """实体复用时的更新逻辑"""
    
    update_props = {
        "updated_at": current_time.isoformat(),
        "reuse_count": "COALESCE(e.reuse_count, 0) + 1"
    }
    
    # 风险分数取最高值
    if entity_data.risk_score > 0:
        update_props["risk_score"] = f"""
        CASE WHEN e.risk_score < {entity_data.risk_score} 
             THEN {entity_data.risk_score} 
             ELSE e.risk_score END
        """
```

### 6.4 关系特征记录

### 6.4.1 证据-实体关系增强

在 `Evidence` → `Entity` 的 `RELATES_TO` 关系中记录详细的上下文信息：


### 6.4.2 关系属性说明

- `is_new_entity`: 布尔值，标记该关系建立时实体是否为新创建
- `entity_context`: JSON字符串，包含实体在关联时的快照信息
- `extraction_reason`: 字符串，说明实体提取的原因
- `properties_snapshot`: 实体属性快照，用于溯源分析

### 6.5 性能优化策略

### 6.5.1 索引设计

为提高匹配效率，在关键匹配字段上建立索引：

```cypher
-- 网络端点实体索引
CREATE INDEX idx_network_endpoint_ip_port IF NOT EXISTS
FOR (n:NetworkEndpointEntity)
ON (n.ip_address, n.port);

-- 文件实体索引
CREATE INDEX idx_file_hash IF NOT EXISTS
FOR (n:FileEntity)
ON (n.file_hash);

CREATE INDEX idx_file_path IF NOT EXISTS
FOR (n:FileEntity)
ON (n.file_path);

-- 进程实体索引
CREATE INDEX idx_process_signature IF NOT EXISTS
FOR (n:ProcessEntity)
ON (n.process_signature);

-- URL实体索引
CREATE INDEX idx_url_full IF NOT EXISTS
FOR (n:UrlEntity)
ON (n.full_url);

-- DNS查询实体索引
CREATE INDEX idx_dns_query IF NOT EXISTS
FOR (n:DnsQueryEntity)
ON (n.query_name, n.query_type);
```

### 6.5.2 匹配优化

- **分层匹配**: 优先使用高区分度字段（如哈希值）进行匹配
- **早期退出**: 匹配条件为空时直接创建新实体
- **批量处理**: 支持批量实体去重处理