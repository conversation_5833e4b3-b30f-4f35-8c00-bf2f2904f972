#!/usr/bin/env python3
"""
简化版告警插入脚本
专门用于API格式的告警数据
"""

import asyncio
import aiohttp
import json
import sys
import time
from typing import Dict, Any, List


class SimpleAPIClient:
    """简化的API客户端"""

    def __init__(self, base_url="http://localhost:8000/api/v1"):
        self.base_url = base_url
        self.session = None
        self.timeout = aiohttp.ClientTimeout(total=30)

    async def __aenter__(self):
        connector = aiohttp.TCPConnector(
            limit=20,
            limit_per_host=10,
            keepalive_timeout=30,
            enable_cleanup_closed=True
        )
        self.session = aiohttp.ClientSession(
            connector=connector,
            timeout=self.timeout,
            headers={'Content-Type': 'application/json'}
        )
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        if self.session:
            await self.session.close()

    async def batch_create_alerts(self, alerts_data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """批量创建告警"""
        url = f"{self.base_url}/alerts/batch"
        
        # 直接传递告警列表，不需要包装在对象中
        try:
            async with self.session.post(url, json=alerts_data) as response:
                if response.status == 200:
                    return await response.json()
                else:
                    error_text = await response.text()
                    raise Exception(f"API请求失败 {response.status}: {error_text}")
        except Exception as e:
            raise Exception(f"请求异常: {e}")

    async def get_alerts_count(self) -> int:
        """获取告警数量"""
        url = f"{self.base_url}/system/stats"
        try:
            async with self.session.get(url) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get('alert_count', 0)
                else:
                    return 0
        except Exception:
            return 0


async def batch_insert_alerts_simple(alerts_data: List[Dict[str, Any]], batch_size: int = 10) -> tuple[int, int]:
    """简化版批量插入告警"""
    print(f"🔄 开始批量插入 {len(alerts_data)} 个告警...")
    
    total_success = 0
    total_failed = 0
    
    async with SimpleAPIClient() as client:
        # 分批处理
        batches = [alerts_data[i:i + batch_size] for i in range(0, len(alerts_data), batch_size)]
        
        for i, batch in enumerate(batches):
            print(f"   处理批次 {i + 1}/{len(batches)} ({len(batch)} 个告警)")
            
            try:
                start_time = time.time()
                result = await client.batch_create_alerts(batch)
                batch_time = time.time() - start_time
                
                # 统计结果
                success_count = result.get('success_count', 0)
                failed_count = result.get('failed_count', 0)
                
                total_success += success_count
                total_failed += failed_count
                
                print(f"   ✅ 批次完成: 成功 {success_count}, 失败 {failed_count}, 耗时: {batch_time:.3f}秒")
                
                # 显示失败详情
                if failed_count > 0 and 'errors' in result:
                    for error in result['errors'][:3]:  # 只显示前3个错误
                        print(f"      ⚠️ 错误: {error.get('alert_detail_id', 'Unknown')} - {error.get('error', 'Unknown error')}")
                
                # 显示成功创建的实体统计
                if 'created_entities' in result:
                    entities = result['created_entities']
                    if entities:
                        print(f"      📊 创建实体: {entities}")
                
            except Exception as e:
                print(f"   ❌ 批次 {i + 1} 失败: {e}")
                total_failed += len(batch)
    
    return total_success, total_failed


def sync_insert_wrapper(alerts_data: List[Dict[str, Any]], batch_size: int = 10) -> tuple[int, int]:
    """同步包装器"""
    return asyncio.run(batch_insert_alerts_simple(alerts_data, batch_size))


def get_alert_count_sync() -> int:
    """同步获取告警数量"""
    import requests
    try:
        # 先尝试新的stats接口
        response = requests.get("http://localhost:8000/api/v1/system/stats", timeout=5)
        if response.status_code == 200:
            data = response.json()
            return data.get('alert_count', 0)
        else:
            # 如果stats接口不可用，使用节点统计接口
            response = requests.get("http://localhost:8000/api/v1/system/database/nodes?label=AlertDetail", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return data.get('count', 0)
            else:
                return 0
    except Exception as e:
        print(f"      ❌ API请求失败: {e}")
        return 0


def insert_alerts_simple(json_file: str) -> bool:
    """简化版告警插入"""
    print("🚀 AlertGraph 简化版告警插入工具")
    print("="*50)
    print(f"📁 数据文件: {json_file}")
    
    total_start_time = time.time()
    
    try:
        # 读取文件
        print(f"\n📁 读取文件...")
        with open(json_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        alerts_data = data.get('alerts', [])
        print(f"📋 发现 {len(alerts_data)} 个告警")
        
        if not alerts_data:
            print("❌ 没有有效的告警数据")
            return False
        
        # 验证数据格式
        print(f"\n🔍 验证数据格式...")
        required_fields = ['alert_detail_id', 'alert_name', 'product_name']
        valid_alerts = []
        
        for i, alert in enumerate(alerts_data):
            if all(field in alert for field in required_fields):
                # 验证evidence_artifacts格式
                if 'evidence_artifacts' in alert and isinstance(alert['evidence_artifacts'], list):
                    if len(alert['evidence_artifacts']) == 1:
                        valid_alerts.append(alert)
                    else:
                        print(f"   ⚠️ 告警 {i+1} ({alert.get('alert_detail_id')}) evidence数量: {len(alert['evidence_artifacts'])}, 建议只有1个")
                        valid_alerts.append(alert)  # 仍然接受
                else:
                    print(f"   ⚠️ 告警 {i+1} 缺少evidence_artifacts字段")
                    valid_alerts.append(alert)  # 仍然接受
            else:
                missing = [field for field in required_fields if field not in alert]
                print(f"   ⚠️ 告警 {i+1} 缺少必需字段: {missing}")
        
        print(f"✅ 有效告警: {len(valid_alerts)}/{len(alerts_data)}")
        
        if not valid_alerts:
            print("❌ 没有有效的告警数据")
            return False
        
        # 检查API服务
        print(f"\n🔍 检查API服务...")
        try:
            import requests
            response = requests.get("http://localhost:8000/api/v1/system/health", timeout=5)
            if response.status_code == 200:
                health_data = response.json()
                print(f"✅ API服务正常 - {health_data.get('status', 'unknown')}")
            else:
                print(f"⚠️ API服务响应异常: {response.status_code}")
        except Exception as e:
            print(f"❌ API服务不可用: {e}")
            return False
        
        # 获取插入前统计
        before_count = get_alert_count_sync()
        print(f"📊 插入前告警数: {before_count}")
        
        # 批量插入
        print(f"\n💾 开始批量插入...")
        insert_start = time.time()
        success_count, failed_count = sync_insert_wrapper(valid_alerts, batch_size=5)
        insert_time = time.time() - insert_start
        
        # 获取插入后统计
        time.sleep(1)  # 等待数据库更新
        after_count = get_alert_count_sync()
        
        # 输出结果
        total_time = time.time() - total_start_time
        print(f"\n📊 插入结果:")
        print(f"   ✅ 成功插入: {success_count}")
        print(f"   ❌ 插入失败: {failed_count}")
        print(f"   📈 告警数量: {before_count} → {after_count}")
        print(f"   📈 新增告警: {after_count - before_count}")
        print(f"   ⏱️ 插入耗时: {insert_time:.3f}秒")
        print(f"   ⏱️ 总耗时: {total_time:.3f}秒")
        
        if success_count > 0:
            avg_time = insert_time / success_count
            print(f"   📈 平均耗时: {avg_time:.3f}秒/告警")
        
        return success_count > 0
        
    except FileNotFoundError:
        print(f"❌ 文件不存在: {json_file}")
        return False
    except json.JSONDecodeError as e:
        print(f"❌ JSON解析失败: {e}")
        return False
    except Exception as e:
        total_time = time.time() - total_start_time
        print(f"❌ 插入过程异常: {e}")
        print(f"总耗时: {total_time:.3f}秒")
        return False


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python scripts/simple_insert_alerts.py <json文件路径>")
        print("示例:")
        print("  python scripts/simple_insert_alerts.py assets/data/api_format_alerts.json")
        sys.exit(1)
    
    json_file = sys.argv[1]
    
    success = insert_alerts_simple(json_file)
    
    if success:
        print("\n🎉 告警插入完成!")
        print("\n💡 提示: 可以通过以下方式验证数据:")
        print("  - 访问 http://localhost:8000/docs 查看API文档")
        print("  - 使用 Cypher 查询 Neo4j 数据库检查实体关系")
        sys.exit(0)
    else:
        print("\n❌ 告警插入失败!")
        sys.exit(1)


if __name__ == "__main__":
    main() 